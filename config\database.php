<?php
/**
 * Database Configuration
 * Sistem Akuntansi Web - Database Setup
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'akuntansi_db';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8")
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }

    public function createDatabase() {
        try {
            // Create database if not exists
            $conn = new PDO("mysql:host=" . $this->host, $this->username, $this->password);
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8 COLLATE utf8_general_ci";
            $conn->exec($sql);
            
            echo "Database created successfully<br>";
            
            // Use the database
            $conn->exec("USE " . $this->db_name);
            
            // Create tables
            $this->createTables($conn);
            
        } catch(PDOException $e) {
            echo "Error creating database: " . $e->getMessage();
        }
    }

    private function createTables($conn) {
        // Chart of Accounts Table
        $sql_coa = "CREATE TABLE IF NOT EXISTS chart_of_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            account_code VARCHAR(20) NOT NULL UNIQUE,
            category VARCHAR(100),
            account_name VARCHAR(200) NOT NULL,
            balance_position ENUM('Db', 'Kr') NOT NULL,
            report_position ENUM('NERACA', 'LABA/RUGI') NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";

        // Journal Entries Table (Jurnal Umum)
        $sql_journal = "CREATE TABLE IF NOT EXISTS journal_entries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            entry_date DATE NOT NULL,
            reference_no VARCHAR(50),
            description TEXT,
            total_debit DECIMAL(15,2) DEFAULT 0,
            total_credit DECIMAL(15,2) DEFAULT 0,
            status ENUM('draft', 'posted') DEFAULT 'draft',
            created_by VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";

        // Journal Details Table (Detail Jurnal)
        $sql_journal_details = "CREATE TABLE IF NOT EXISTS journal_details (
            id INT AUTO_INCREMENT PRIMARY KEY,
            journal_id INT NOT NULL,
            account_code VARCHAR(20) NOT NULL,
            description TEXT,
            debit_amount DECIMAL(15,2) DEFAULT 0,
            credit_amount DECIMAL(15,2) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (journal_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
            FOREIGN KEY (account_code) REFERENCES chart_of_accounts(account_code) ON UPDATE CASCADE
        )";

        // Bank Transactions Table (Buku Bank)
        $sql_bank = "CREATE TABLE IF NOT EXISTS bank_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            bank_account VARCHAR(50) NOT NULL,
            transaction_date DATE NOT NULL,
            reference_no VARCHAR(50),
            description TEXT,
            debit_amount DECIMAL(15,2) DEFAULT 0,
            credit_amount DECIMAL(15,2) DEFAULT 0,
            balance DECIMAL(15,2) DEFAULT 0,
            status ENUM('cleared', 'pending', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        // Trial Balance View (for reports)
        $sql_view_trial_balance = "CREATE OR REPLACE VIEW trial_balance AS
        SELECT 
            coa.account_code,
            coa.account_name,
            coa.category,
            coa.balance_position,
            coa.report_position,
            COALESCE(SUM(jd.debit_amount), 0) as total_debit,
            COALESCE(SUM(jd.credit_amount), 0) as total_credit,
            CASE 
                WHEN coa.balance_position = 'Db' THEN 
                    COALESCE(SUM(jd.debit_amount), 0) - COALESCE(SUM(jd.credit_amount), 0)
                ELSE 
                    COALESCE(SUM(jd.credit_amount), 0) - COALESCE(SUM(jd.debit_amount), 0)
            END as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_details jd ON coa.account_code = jd.account_code
        LEFT JOIN journal_entries je ON jd.journal_id = je.id AND je.status = 'posted'
        WHERE coa.is_active = TRUE
        GROUP BY coa.account_code, coa.account_name, coa.category, coa.balance_position, coa.report_position";

        try {
            $conn->exec($sql_coa);
            echo "Chart of Accounts table created<br>";
            
            $conn->exec($sql_journal);
            echo "Journal Entries table created<br>";
            
            $conn->exec($sql_journal_details);
            echo "Journal Details table created<br>";
            
            $conn->exec($sql_bank);
            echo "Bank Transactions table created<br>";
            
            $conn->exec($sql_view_trial_balance);
            echo "Trial Balance view created<br>";
            
        } catch(PDOException $e) {
            echo "Error creating tables: " . $e->getMessage();
        }
    }
}

// Auto-create database and tables if accessed directly
if (basename($_SERVER['PHP_SELF']) == 'database.php') {
    $database = new Database();
    $database->createDatabase();
}
?>