# Sistem Akuntansi Web

Sistem akuntansi berbasis web yang dibuat dengan PHP native, terinspirasi dari format Excel CSV Chart of Accounts yang sudah ada.

## 🚀 Fitur Utama

### 📊 Master Data
- **Chart of Accounts (COA)** - Kelola master data akun
- **Import/Export CSV** - Import dari Excel/CSV dan export data
- **Kategorisasi Akun** - Pengelompokan akun berdasarkan departemen

### 📝 Transaksi
- **Jurnal Umum** - Input jurnal harian
- **Buku Bank** - Pencatatan transaksi bank
- **Validasi Otomatis** - Validasi debit = kredit

### 📈 Laporan Keuangan
- **Trial Balance** - Neraca saldo
- **Balance Sheet (Neraca)** - Laporan posisi keuangan
- **Income Statement (Laba Rugi)** - Laporan laba rugi
- **Cash Flow (Arus Kas)** - Laporan arus kas

### 🎨 Interface
- **Admin Panel Elegant** - Interface modern dan responsif
- **Dashboard Interaktif** - Grafik dan statistik real-time
- **Mobile Friendly** - Responsive design untuk semua device

## 🛠️ Teknologi

- **Backend**: PHP 7.4+ (Native, tanpa framework)
- **Database**: MySQL 5.7+
- **Frontend**: Bootstrap 5, Chart.js, DataTables
- **Icons**: Bootstrap Icons
- **Server**: Apache/Nginx (Laragon/XAMPP)

## 📋 Persyaratan Sistem

- PHP 7.4 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Apache/Nginx web server
- Extension PHP: PDO, PDO_MySQL

## 🚀 Instalasi

### 1. Clone/Download Project
```bash
# Jika menggunakan Git
git clone [repository-url] akuntansi

# Atau download dan extract ke folder akuntansi
```

### 2. Setup Database
```bash
# Edit konfigurasi database
nano config/database.php

# Sesuaikan:
private $host = 'localhost';
private $db_name = 'akuntansi_db';
private $username = 'root';
private $password = '';
```

### 3. Jalankan Setup
```bash
# Akses melalui browser
http://localhost/akuntansi/setup.php
```

### 4. Import Data CSV (Opsional)
Jika Anda memiliki file CSV Chart of Accounts:
- Letakkan file CSV di root folder dengan nama `format lk gdhi.csv`
- Jalankan setup.php, data akan otomatis diimport

### 5. Akses Admin Panel
```bash
http://localhost/akuntansi/admin/
```

## 📁 Struktur Folder

```
akuntansi/
├── admin/                  # Admin panel
│   ├── index.php          # Dashboard
│   ├── chart-of-accounts.php
│   ├── journal-entries.php
│   ├── import-export.php
│   └── ...
├── classes/               # PHP Classes
│   ├── ChartOfAccounts.php
│   ├── Journal.php
│   └── ...
├── config/               # Konfigurasi
│   └── database.php
├── uploads/              # Upload files
├── exports/              # Export files
├── setup.php            # Setup installer
└── README.md
```

## 🎯 Cara Penggunaan

### 1. Setup Chart of Accounts
1. Masuk ke **Chart of Accounts**
2. Tambah akun baru atau import dari CSV
3. Atur kategori dan posisi saldo

### 2. Input Transaksi
1. Masuk ke **Jurnal Umum**
2. Buat jurnal entry baru
3. Pilih akun debit dan kredit
4. Pastikan total debit = kredit

### 3. Generate Laporan
1. **Trial Balance** - Lihat saldo semua akun
2. **Neraca** - Laporan posisi keuangan
3. **Laba Rugi** - Laporan pendapatan dan biaya

### 4. Import/Export Data
1. **Import CSV** - Upload file CSV dengan format yang benar
2. **Export CSV** - Download data untuk backup/analisis
3. **Template** - Download template kosong

## 📊 Format CSV Import

Format CSV yang didukung (separator: semicolon `;`):

```csv
no. account;KATEGORI;no. account;NAMA COA;POS SALDO;POS LAPORAN;;;;
1-01-11000;;1-01-11000;KAS BESAR;Db;NERACA;;;;
1-02-11001;;1-02-11001;KAS KECIL;Db;NERACA;;;;
```

### Kolom Wajib:
- **no. account** - Kode akun unik
- **NAMA COA** - Nama akun
- **POS SALDO** - Db (Debit) atau Kr (Kredit)
- **POS LAPORAN** - NERACA atau LABA/RUGI

## 🔧 Konfigurasi

### Database
Edit `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'akuntansi_db';
private $username = 'root';
private $password = '';
```

### Upload Limits
Untuk file CSV besar, edit `php.ini`:
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
```

## 🐛 Troubleshooting

### Error Database Connection
- Pastikan MySQL server berjalan
- Cek username/password database
- Pastikan database sudah dibuat

### Error Import CSV
- Pastikan format CSV benar (separator `;`)
- Cek encoding file (UTF-8)
- Pastikan kolom wajib terisi

### Error Permission
- Pastikan folder `uploads/` dan `exports/` writable
- Set permission 755 atau 777 jika perlu

## 📝 Changelog

### Version 1.0.0
- ✅ Chart of Accounts management
- ✅ CSV Import/Export
- ✅ Journal Entries
- ✅ Trial Balance
- ✅ Balance Sheet & Income Statement
- ✅ Responsive admin interface
- ✅ Dashboard with charts

## 🤝 Kontribusi

Kontribusi sangat diterima! Silakan:
1. Fork repository
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Buat Pull Request

## 📄 Lisensi

Project ini menggunakan lisensi MIT. Lihat file `LICENSE` untuk detail.

## 📞 Support

Jika ada pertanyaan atau butuh bantuan:
- Buat issue di repository
- Email: [<EMAIL>]

## 🙏 Acknowledgments

- Terinspirasi dari format Excel Chart of Accounts GDHI
- Bootstrap untuk UI framework
- Chart.js untuk visualisasi data
- DataTables untuk tabel interaktif

---

**Dibuat dengan ❤️ menggunakan PHP Native**