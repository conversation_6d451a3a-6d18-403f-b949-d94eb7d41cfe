<?php
/**
 * Import/Export Data
 * Import CSV dan Export data
 */

session_start();
require_once '../classes/ChartOfAccounts.php';

$coa = new ChartOfAccounts();
$message = '';
$message_type = '';

// Handle CSV Import
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'import_csv') {
    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] == 0) {
        $upload_dir = '../uploads/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        $file_name = time() . '_' . $_FILES['csv_file']['name'];
        $file_path = $upload_dir . $file_name;
        
        if (move_uploaded_file($_FILES['csv_file']['tmp_name'], $file_path)) {
            try {
                $result = $coa->importFromCSV($file_path);
                $message = "Import berhasil! {$result['success']} akun berhasil diimport.";
                if ($result['errors'] > 0) {
                    $message .= " {$result['errors']} baris gagal diimport.";
                }
                $message_type = 'success';
                
                // Clean up uploaded file
                unlink($file_path);
            } catch (Exception $e) {
                $message = 'Error importing CSV: ' . $e->getMessage();
                $message_type = 'danger';
            }
        } else {
            $message = 'Error uploading file';
            $message_type = 'danger';
        }
    } else {
        $message = 'Please select a CSV file';
        $message_type = 'warning';
    }
}

// Handle Export
if (isset($_GET['action']) && $_GET['action'] == 'export_csv') {
    try {
        $filename = $coa->exportToCSV();
        $filepath = '../exports/' . $filename;
        
        if (file_exists($filepath)) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($filepath));
            readfile($filepath);
            
            // Clean up exported file
            unlink($filepath);
            exit;
        }
    } catch (Exception $e) {
        $message = 'Error exporting data: ' . $e->getMessage();
        $message_type = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import/Export - Sistem Akuntansi</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
        }

        .content-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .btn-elegant {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-elegant:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: var(--secondary-color);
            background-color: rgba(52, 152, 219, 0.05);
        }

        .upload-area.dragover {
            border-color: var(--success-color);
            background-color: rgba(39, 174, 96, 0.1);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--secondary-color), #5dade2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .format-example {
            background-color: #f8f9fa;
            border-left: 4px solid var(--secondary-color);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: var(--secondary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4">
                        <h4 class="text-white mb-4">
                            <i class="bi bi-calculator"></i> Akuntansi
                        </h4>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-speedometer2 me-2"></i> Dashboard
                        </a>
                        <a class="nav-link" href="chart-of-accounts.php">
                            <i class="bi bi-list-ul me-2"></i> Chart of Accounts
                        </a>
                        <a class="nav-link" href="journal-entries.php">
                            <i class="bi bi-journal-text me-2"></i> Jurnal Umum
                        </a>
                        <a class="nav-link" href="trial-balance.php">
                            <i class="bi bi-table me-2"></i> Trial Balance
                        </a>
                        <a class="nav-link" href="balance-sheet.php">
                            <i class="bi bi-bar-chart me-2"></i> Neraca
                        </a>
                        <a class="nav-link" href="income-statement.php">
                            <i class="bi bi-graph-up me-2"></i> Laba Rugi
                        </a>
                        <a class="nav-link" href="bank-transactions.php">
                            <i class="bi bi-bank me-2"></i> Buku Bank
                        </a>
                        <a class="nav-link" href="reports.php">
                            <i class="bi bi-file-earmark-text me-2"></i> Laporan
                        </a>
                        <hr class="text-white-50">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear me-2"></i> Pengaturan
                        </a>
                        <a class="nav-link active" href="import-export.php">
                            <i class="bi bi-arrow-down-up me-2"></i> Import/Export
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">Import/Export Data</h1>
                            <p class="text-muted">Import data dari CSV atau export data ke CSV</p>
                        </div>
                    </div>

                    <!-- Alert Messages -->
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <!-- Import Section -->
                        <div class="col-md-6">
                            <div class="content-card">
                                <div class="feature-icon">
                                    <i class="bi bi-upload"></i>
                                </div>
                                <h4 class="text-center mb-4">Import Chart of Accounts</h4>
                                
                                <form method="POST" enctype="multipart/form-data" id="importForm">
                                    <input type="hidden" name="action" value="import_csv">
                                    
                                    <div class="upload-area" id="uploadArea">
                                        <i class="bi bi-cloud-upload text-muted" style="font-size: 3rem;"></i>
                                        <h5 class="mt-3">Drag & Drop CSV File</h5>
                                        <p class="text-muted">atau klik untuk memilih file</p>
                                        <input type="file" class="form-control" name="csv_file" id="csvFile" 
                                               accept=".csv" style="display: none;" required>
                                        <button type="button" class="btn btn-outline-primary btn-elegant" 
                                                onclick="document.getElementById('csvFile').click()">
                                            Pilih File CSV
                                        </button>
                                    </div>
                                    
                                    <div class="mt-3" id="fileInfo" style="display: none;">
                                        <div class="alert alert-info">
                                            <i class="bi bi-file-earmark-text"></i>
                                            <span id="fileName"></span>
                                            <button type="button" class="btn-close float-end" onclick="clearFile()"></button>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid mt-4">
                                        <button type="submit" class="btn btn-success btn-elegant" id="importBtn" disabled>
                                            <i class="bi bi-upload me-2"></i> Import Data
                                        </button>
                                    </div>
                                </form>

                                <!-- Format Guide -->
                                <div class="mt-4">
                                    <h6>Format CSV yang Dibutuhkan:</h6>
                                    <div class="format-example">
no. account;KATEGORI;no. account;NAMA COA;POS SALDO;POS LAPORAN;;;;
1-01-11000;;1-01-11000;KAS BESAR;Db;NERACA;;;;
1-02-11001;;1-02-11001;KAS KECIL;Db;NERACA;;;;
                                    </div>
                                    <small class="text-muted">
                                        * Separator: semicolon (;)<br>
                                        * Encoding: UTF-8<br>
                                        * Kolom wajib: Kode Akun, Nama Akun, Posisi Saldo, Posisi Laporan
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Export Section -->
                        <div class="col-md-6">
                            <div class="content-card">
                                <div class="feature-icon bg-success">
                                    <i class="bi bi-download"></i>
                                </div>
                                <h4 class="text-center mb-4">Export Chart of Accounts</h4>
                                
                                <p class="text-center text-muted mb-4">
                                    Download semua data Chart of Accounts dalam format CSV
                                </p>
                                
                                <div class="d-grid">
                                    <a href="?action=export_csv" class="btn btn-success btn-elegant">
                                        <i class="bi bi-download me-2"></i> Download CSV
                                    </a>
                                </div>

                                <!-- Export Info -->
                                <div class="mt-4">
                                    <h6>File yang akan didownload berisi:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check-circle text-success me-2"></i> Semua kode akun</li>
                                        <li><i class="bi bi-check-circle text-success me-2"></i> Nama akun lengkap</li>
                                        <li><i class="bi bi-check-circle text-success me-2"></i> Kategori akun</li>
                                        <li><i class="bi bi-check-circle text-success me-2"></i> Posisi saldo & laporan</li>
                                        <li><i class="bi bi-check-circle text-success me-2"></i> Status aktif/non-aktif</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="content-card">
                        <h5 class="mb-4">Panduan Import/Export</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Langkah Import:</h6>
                                <div class="d-flex align-items-start mb-3">
                                    <div class="step-number">1</div>
                                    <div>
                                        <strong>Siapkan File CSV</strong><br>
                                        <small class="text-muted">Format sesuai template dengan separator semicolon (;)</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-start mb-3">
                                    <div class="step-number">2</div>
                                    <div>
                                        <strong>Upload File</strong><br>
                                        <small class="text-muted">Drag & drop atau klik untuk memilih file CSV</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-start mb-3">
                                    <div class="step-number">3</div>
                                    <div>
                                        <strong>Import Data</strong><br>
                                        <small class="text-muted">Klik tombol Import untuk memproses data</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-success">Langkah Export:</h6>
                                <div class="d-flex align-items-start mb-3">
                                    <div class="step-number bg-success">1</div>
                                    <div>
                                        <strong>Klik Download CSV</strong><br>
                                        <small class="text-muted">File akan otomatis terdownload</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-start mb-3">
                                    <div class="step-number bg-success">2</div>
                                    <div>
                                        <strong>Buka dengan Excel</strong><br>
                                        <small class="text-muted">File dapat dibuka dengan Excel atau aplikasi spreadsheet lainnya</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-start mb-3">
                                    <div class="step-number bg-success">3</div>
                                    <div>
                                        <strong>Edit & Re-import</strong><br>
                                        <small class="text-muted">Edit data dan import kembali jika diperlukan</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sample CSV Download -->
                    <div class="content-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-2">Butuh Template CSV?</h6>
                                <p class="text-muted mb-0">Download template CSV kosong untuk memudahkan input data</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="download-template.php" class="btn btn-outline-primary btn-elegant">
                                    <i class="bi bi-file-earmark-arrow-down me-2"></i> Download Template
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const csvFile = document.getElementById('csvFile');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const importBtn = document.getElementById('importBtn');

        // Drag and drop events
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'text/csv') {
                csvFile.files = files;
                showFileInfo(files[0]);
            } else {
                alert('Please select a CSV file');
            }
        });

        // File input change
        csvFile.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                showFileInfo(e.target.files[0]);
            }
        });

        // Show file info
        function showFileInfo(file) {
            fileName.textContent = file.name + ' (' + formatFileSize(file.size) + ')';
            fileInfo.style.display = 'block';
            importBtn.disabled = false;
        }

        // Clear file
        function clearFile() {
            csvFile.value = '';
            fileInfo.style.display = 'none';
            importBtn.disabled = true;
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Form submission
        document.getElementById('importForm').addEventListener('submit', (e) => {
            if (!csvFile.files.length) {
                e.preventDefault();
                alert('Please select a CSV file');
                return;
            }
            
            importBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i> Importing...';
            importBtn.disabled = true;
        });
    </script>
</body>
</html>