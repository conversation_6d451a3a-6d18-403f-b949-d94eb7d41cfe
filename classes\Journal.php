<?php
/**
 * Journal Class
 * Mengelola Jurnal Umum dan Detail Jurnal
 */

require_once '../config/database.php';

class Journal {
    private $conn;
    private $journal_table = "journal_entries";
    private $detail_table = "journal_details";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Create new journal entry
    public function createJournalEntry($data) {
        try {
            $this->conn->beginTransaction();
            
            // Insert journal header
            $query = "INSERT INTO " . $this->journal_table . " 
                     (entry_date, reference_no, description, total_debit, total_credit, created_by) 
                     VALUES (?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([
                $data['entry_date'],
                $data['reference_no'],
                $data['description'],
                $data['total_debit'],
                $data['total_credit'],
                $data['created_by'] ?? 'admin'
            ]);
            
            $journal_id = $this->conn->lastInsertId();
            
            // Insert journal details
            $detail_query = "INSERT INTO " . $this->detail_table . " 
                           (journal_id, account_code, description, debit_amount, credit_amount) 
                           VALUES (?, ?, ?, ?, ?)";
            
            $detail_stmt = $this->conn->prepare($detail_query);
            
            foreach ($data['details'] as $detail) {
                $detail_stmt->execute([
                    $journal_id,
                    $detail['account_code'],
                    $detail['description'],
                    $detail['debit_amount'] ?? 0,
                    $detail['credit_amount'] ?? 0
                ]);
            }
            
            $this->conn->commit();
            return $journal_id;
            
        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    // Get all journal entries
    public function getAllJournalEntries($limit = 50, $offset = 0, $filters = []) {
        $query = "SELECT je.*, 
                         COUNT(jd.id) as detail_count,
                         GROUP_CONCAT(CONCAT(coa.account_code, ' - ', coa.account_name) SEPARATOR '; ') as accounts
                  FROM " . $this->journal_table . " je
                  LEFT JOIN " . $this->detail_table . " jd ON je.id = jd.journal_id
                  LEFT JOIN chart_of_accounts coa ON jd.account_code = coa.account_code
                  WHERE 1=1";
        
        $params = [];
        
        // Apply filters
        if (!empty($filters['date_from'])) {
            $query .= " AND je.entry_date >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $query .= " AND je.entry_date <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (!empty($filters['status'])) {
            $query .= " AND je.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['reference'])) {
            $query .= " AND je.reference_no LIKE ?";
            $params[] = "%{$filters['reference']}%";
        }
        
        $query .= " GROUP BY je.id ORDER BY je.entry_date DESC, je.id DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get journal entry by ID with details
    public function getJournalEntryById($id) {
        // Get header
        $query = "SELECT * FROM " . $this->journal_table . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        $journal = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$journal) return null;
        
        // Get details
        $detail_query = "SELECT jd.*, coa.account_name 
                        FROM " . $this->detail_table . " jd
                        LEFT JOIN chart_of_accounts coa ON jd.account_code = coa.account_code
                        WHERE jd.journal_id = ?
                        ORDER BY jd.id";
        
        $detail_stmt = $this->conn->prepare($detail_query);
        $detail_stmt->execute([$id]);
        $journal['details'] = $detail_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $journal;
    }

    // Post journal entry (change status to posted)
    public function postJournalEntry($id) {
        $query = "UPDATE " . $this->journal_table . " SET status = 'posted' WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        
        return $stmt->execute([$id]);
    }

    // Unpost journal entry (change status to draft)
    public function unpostJournalEntry($id) {
        $query = "UPDATE " . $this->journal_table . " SET status = 'draft' WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        
        return $stmt->execute([$id]);
    }

    // Delete journal entry
    public function deleteJournalEntry($id) {
        try {
            $this->conn->beginTransaction();
            
            // Delete details first
            $detail_query = "DELETE FROM " . $this->detail_table . " WHERE journal_id = ?";
            $detail_stmt = $this->conn->prepare($detail_query);
            $detail_stmt->execute([$id]);
            
            // Delete header
            $header_query = "DELETE FROM " . $this->journal_table . " WHERE id = ?";
            $header_stmt = $this->conn->prepare($header_query);
            $header_stmt->execute([$id]);
            
            $this->conn->commit();
            return true;
            
        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    // Get trial balance
    public function getTrialBalance($date_to = null) {
        $query = "SELECT * FROM trial_balance";
        $params = [];
        
        if ($date_to) {
            // Add date filter to the view query
            $query = "SELECT 
                        coa.account_code,
                        coa.account_name,
                        coa.category,
                        coa.balance_position,
                        coa.report_position,
                        COALESCE(SUM(jd.debit_amount), 0) as total_debit,
                        COALESCE(SUM(jd.credit_amount), 0) as total_credit,
                        CASE 
                            WHEN coa.balance_position = 'Db' THEN 
                                COALESCE(SUM(jd.debit_amount), 0) - COALESCE(SUM(jd.credit_amount), 0)
                            ELSE 
                                COALESCE(SUM(jd.credit_amount), 0) - COALESCE(SUM(jd.debit_amount), 0)
                        END as balance
                      FROM chart_of_accounts coa
                      LEFT JOIN journal_details jd ON coa.account_code = jd.account_code
                      LEFT JOIN journal_entries je ON jd.journal_id = je.id 
                      WHERE coa.is_active = TRUE 
                      AND (je.status = 'posted' OR je.id IS NULL)
                      AND (je.entry_date <= ? OR je.entry_date IS NULL)
                      GROUP BY coa.account_code, coa.account_name, coa.category, coa.balance_position, coa.report_position
                      ORDER BY coa.account_code";
            $params[] = $date_to;
        } else {
            $query .= " ORDER BY account_code";
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Generate balance sheet
    public function getBalanceSheet($date_to = null) {
        $trial_balance = $this->getTrialBalance($date_to);
        
        $balance_sheet = [
            'assets' => [],
            'liabilities' => [],
            'equity' => [],
            'total_assets' => 0,
            'total_liabilities' => 0,
            'total_equity' => 0
        ];
        
        foreach ($trial_balance as $account) {
            if ($account['report_position'] == 'NERACA') {
                $balance = floatval($account['balance']);
                
                // Classify accounts based on account code pattern
                $code = $account['account_code'];
                
                if (preg_match('/^[1-3]/', $code)) {
                    // Assets (codes starting with 1, 2, 3)
                    $balance_sheet['assets'][] = $account;
                    $balance_sheet['total_assets'] += $balance;
                } elseif (preg_match('/^[4-5]/', $code)) {
                    // Liabilities (codes starting with 4, 5)
                    $balance_sheet['liabilities'][] = $account;
                    $balance_sheet['total_liabilities'] += $balance;
                } elseif (preg_match('/^6/', $code)) {
                    // Equity (codes starting with 6)
                    $balance_sheet['equity'][] = $account;
                    $balance_sheet['total_equity'] += $balance;
                }
            }
        }
        
        return $balance_sheet;
    }

    // Generate income statement
    public function getIncomeStatement($date_from = null, $date_to = null) {
        $query = "SELECT 
                    coa.account_code,
                    coa.account_name,
                    coa.category,
                    coa.balance_position,
                    COALESCE(SUM(jd.debit_amount), 0) as total_debit,
                    COALESCE(SUM(jd.credit_amount), 0) as total_credit,
                    CASE 
                        WHEN coa.balance_position = 'Kr' THEN 
                            COALESCE(SUM(jd.credit_amount), 0) - COALESCE(SUM(jd.debit_amount), 0)
                        ELSE 
                            COALESCE(SUM(jd.debit_amount), 0) - COALESCE(SUM(jd.credit_amount), 0)
                    END as balance
                  FROM chart_of_accounts coa
                  LEFT JOIN journal_details jd ON coa.account_code = jd.account_code
                  LEFT JOIN journal_entries je ON jd.journal_id = je.id 
                  WHERE coa.is_active = TRUE 
                  AND coa.report_position = 'LABA/RUGI'
                  AND (je.status = 'posted' OR je.id IS NULL)";
        
        $params = [];
        
        if ($date_from) {
            $query .= " AND (je.entry_date >= ? OR je.entry_date IS NULL)";
            $params[] = $date_from;
        }
        
        if ($date_to) {
            $query .= " AND (je.entry_date <= ? OR je.entry_date IS NULL)";
            $params[] = $date_to;
        }
        
        $query .= " GROUP BY coa.account_code, coa.account_name, coa.category, coa.balance_position
                   ORDER BY coa.account_code";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $income_statement = [
            'revenue' => [],
            'expenses' => [],
            'total_revenue' => 0,
            'total_expenses' => 0,
            'net_income' => 0
        ];
        
        foreach ($accounts as $account) {
            $balance = floatval($account['balance']);
            $code = $account['account_code'];
            
            if (preg_match('/^7/', $code)) {
                // Revenue (codes starting with 7)
                $income_statement['revenue'][] = $account;
                $income_statement['total_revenue'] += $balance;
            } else {
                // Expenses (codes starting with 8, 50-98)
                $income_statement['expenses'][] = $account;
                $income_statement['total_expenses'] += abs($balance);
            }
        }
        
        $income_statement['net_income'] = $income_statement['total_revenue'] - $income_statement['total_expenses'];
        
        return $income_statement;
    }

    // Get journal statistics
    public function getStatistics() {
        $stats = [];
        
        // Total entries
        $query = "SELECT COUNT(*) as total FROM " . $this->journal_table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total_entries'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Posted vs Draft
        $query = "SELECT status, COUNT(*) as count FROM " . $this->journal_table . " GROUP BY status";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // This month entries
        $query = "SELECT COUNT(*) as count FROM " . $this->journal_table . " 
                 WHERE MONTH(entry_date) = MONTH(CURRENT_DATE()) 
                 AND YEAR(entry_date) = YEAR(CURRENT_DATE())";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['this_month'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        return $stats;
    }

    // Validate journal entry (debit = credit)
    public function validateJournalEntry($details) {
        $total_debit = 0;
        $total_credit = 0;
        
        foreach ($details as $detail) {
            $total_debit += floatval($detail['debit_amount'] ?? 0);
            $total_credit += floatval($detail['credit_amount'] ?? 0);
        }
        
        return [
            'is_balanced' => abs($total_debit - $total_credit) < 0.01,
            'total_debit' => $total_debit,
            'total_credit' => $total_credit,
            'difference' => $total_debit - $total_credit
        ];
    }
}
?>