<?php
/**
 * Get Account Data via AJAX
 * Helper untuk mengambil data akun
 */

header('Content-Type: application/json');

require_once '../classes/ChartOfAccounts.php';

if (!isset($_GET['code'])) {
    echo json_encode(['success' => false, 'message' => 'Account code required']);
    exit;
}

try {
    $coa = new ChartOfAccounts();
    $account = $coa->getAccountByCode($_GET['code']);
    
    if ($account) {
        echo json_encode(['success' => true, 'account' => $account]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Account not found']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>