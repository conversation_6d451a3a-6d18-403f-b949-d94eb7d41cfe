<?php
/**
 * Chart of Accounts Class
 * Mengelola Master Data Akun (COA)
 */

require_once '../config/database.php';

class ChartOfAccounts {
    private $conn;
    private $table_name = "chart_of_accounts";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Import COA from CSV (seperti format Excel Anda)
    public function importFromCSV($csv_file) {
        $results = array();
        $success_count = 0;
        $error_count = 0;

        if (($handle = fopen($csv_file, "r")) !== FALSE) {
            $row = 0;
            while (($data = fgetcsv($handle, 1000, ";")) !== FALSE) {
                $row++;
                
                // Skip header row
                if ($row == 1) continue;
                
                // Skip empty rows
                if (empty($data[0]) && empty($data[3])) continue;
                
                try {
                    // Parse CSV data (sesuai format Excel Anda)
                    $account_code = trim($data[0]);
                    $category = trim($data[1]);
                    $account_name = trim($data[3]);
                    $balance_position = trim($data[4]);
                    $report_position = trim($data[5]);
                    
                    // Validate required fields
                    if (empty($account_code) || empty($account_name)) {
                        continue;
                    }
                    
                    // Set default values
                    if (empty($balance_position)) $balance_position = 'Db';
                    if (empty($report_position)) $report_position = 'NERACA';
                    
                    // Insert to database
                    $query = "INSERT INTO " . $this->table_name . " 
                             (account_code, category, account_name, balance_position, report_position) 
                             VALUES (?, ?, ?, ?, ?)
                             ON DUPLICATE KEY UPDATE 
                             category = VALUES(category),
                             account_name = VALUES(account_name),
                             balance_position = VALUES(balance_position),
                             report_position = VALUES(report_position)";
                    
                    $stmt = $this->conn->prepare($query);
                    $stmt->execute([
                        $account_code, 
                        $category, 
                        $account_name, 
                        $balance_position, 
                        $report_position
                    ]);
                    
                    $success_count++;
                    
                } catch (Exception $e) {
                    $error_count++;
                    $results[] = "Row $row: " . $e->getMessage();
                }
            }
            fclose($handle);
        }
        
        return array(
            'success' => $success_count,
            'errors' => $error_count,
            'messages' => $results
        );
    }

    // Get all accounts
    public function getAllAccounts($search = '', $category = '', $report_position = '') {
        $query = "SELECT * FROM " . $this->table_name . " WHERE is_active = TRUE";
        $params = array();
        
        if (!empty($search)) {
            $query .= " AND (account_code LIKE ? OR account_name LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($category)) {
            $query .= " AND category = ?";
            $params[] = $category;
        }
        
        if (!empty($report_position)) {
            $query .= " AND report_position = ?";
            $params[] = $report_position;
        }
        
        $query .= " ORDER BY account_code";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get account by code
    public function getAccountByCode($account_code) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE account_code = ? AND is_active = TRUE";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$account_code]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Add new account
    public function addAccount($data) {
        $query = "INSERT INTO " . $this->table_name . " 
                 (account_code, category, account_name, balance_position, report_position) 
                 VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        
        return $stmt->execute([
            $data['account_code'],
            $data['category'],
            $data['account_name'],
            $data['balance_position'],
            $data['report_position']
        ]);
    }

    // Update account
    public function updateAccount($id, $data) {
        $query = "UPDATE " . $this->table_name . " 
                 SET category = ?, account_name = ?, balance_position = ?, report_position = ?
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        
        return $stmt->execute([
            $data['category'],
            $data['account_name'],
            $data['balance_position'],
            $data['report_position'],
            $id
        ]);
    }

    // Delete account (soft delete)
    public function deleteAccount($id) {
        $query = "UPDATE " . $this->table_name . " SET is_active = FALSE WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        
        return $stmt->execute([$id]);
    }

    // Get categories
    public function getCategories() {
        $query = "SELECT DISTINCT category FROM " . $this->table_name . " 
                 WHERE category IS NOT NULL AND category != '' AND is_active = TRUE 
                 ORDER BY category";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    // Get accounts for dropdown (formatted)
    public function getAccountsForDropdown($report_position = '') {
        $query = "SELECT account_code, CONCAT(account_code, ' - ', account_name) as display_name 
                 FROM " . $this->table_name . " 
                 WHERE is_active = TRUE";
        
        if (!empty($report_position)) {
            $query .= " AND report_position = ?";
            $params = [$report_position];
        } else {
            $params = [];
        }
        
        $query .= " ORDER BY account_code";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Export to CSV
    public function exportToCSV() {
        $accounts = $this->getAllAccounts();
        
        $filename = 'chart_of_accounts_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = '../exports/' . $filename;
        
        // Create exports directory if not exists
        if (!file_exists('../exports/')) {
            mkdir('../exports/', 0777, true);
        }
        
        $file = fopen($filepath, 'w');
        
        // Write header
        fputcsv($file, [
            'Account Code', 'Category', 'Account Name', 
            'Balance Position', 'Report Position', 'Status'
        ], ';');
        
        // Write data
        foreach ($accounts as $account) {
            fputcsv($file, [
                $account['account_code'],
                $account['category'],
                $account['account_name'],
                $account['balance_position'],
                $account['report_position'],
                $account['is_active'] ? 'Active' : 'Inactive'
            ], ';');
        }
        
        fclose($file);
        
        return $filename;
    }

    // Get statistics
    public function getStatistics() {
        $stats = array();
        
        // Total accounts
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE is_active = TRUE";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total_accounts'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // By report position
        $query = "SELECT report_position, COUNT(*) as count 
                 FROM " . $this->table_name . " 
                 WHERE is_active = TRUE 
                 GROUP BY report_position";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['by_report'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // By balance position
        $query = "SELECT balance_position, COUNT(*) as count 
                 FROM " . $this->table_name . " 
                 WHERE is_active = TRUE 
                 GROUP BY balance_position";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['by_balance'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $stats;
    }
}
?>