<?php
/**
 * Setup Database dan Import Data Awal
 * Script untuk inisialisasi sistem
 */

require_once 'config/database.php';
require_once 'classes/ChartOfAccounts.php';

echo "<h2>Setup Sistem Akuntansi</h2>";
echo "<hr>";

try {
    // 1. Create Database and Tables
    echo "<h3>1. Membuat Database dan Tabel...</h3>";
    $database = new Database();
    $database->createDatabase();
    echo "<p style='color: green;'>✓ Database dan tabel berhasil dibuat</p>";
    
    // 2. Import CSV Data
    echo "<h3>2. Import Data Chart of Accounts...</h3>";
    $coa = new ChartOfAccounts();
    
    // Check if CSV file exists
    $csv_file = 'format lk gdhi.csv';
    if (file_exists($csv_file)) {
        $result = $coa->importFromCSV($csv_file);
        echo "<p style='color: green;'>✓ Import berhasil: {$result['success']} akun diimport</p>";
        
        if ($result['errors'] > 0) {
            echo "<p style='color: orange;'>⚠ {$result['errors']} baris gagal diimport</p>";
            if (!empty($result['messages'])) {
                echo "<details><summary>Detail Error:</summary>";
                foreach ($result['messages'] as $msg) {
                    echo "<p style='color: red; font-size: 12px;'>$msg</p>";
                }
                echo "</details>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠ File CSV tidak ditemukan, membuat data sample...</p>";
        
        // Create sample data
        $sample_accounts = [
            ['account_code' => '1-01-11000', 'category' => '', 'account_name' => 'KAS BESAR', 'balance_position' => 'Db', 'report_position' => 'NERACA'],
            ['account_code' => '1-02-11001', 'category' => '', 'account_name' => 'KAS KECIL', 'balance_position' => 'Db', 'report_position' => 'NERACA'],
            ['account_code' => '1-03-12001', 'category' => 'BANK', 'account_name' => 'BANK BCA', 'balance_position' => 'Db', 'report_position' => 'NERACA'],
            ['account_code' => '1-04-13000', 'category' => 'PIUTANG USAHA', 'account_name' => 'PIUTANG DAGANG', 'balance_position' => 'Db', 'report_position' => 'NERACA'],
            ['account_code' => '4-15-21001', 'category' => 'HUTANG USAHA', 'account_name' => 'HUTANG DAGANG', 'balance_position' => 'Kr', 'report_position' => 'NERACA'],
            ['account_code' => '6-24-31001', 'category' => 'MODAL DISETOR', 'account_name' => 'MODAL SAHAM', 'balance_position' => 'Kr', 'report_position' => 'NERACA'],
            ['account_code' => '7-29-40001', 'category' => '', 'account_name' => 'PENDAPATAN PENJUALAN', 'balance_position' => 'Kr', 'report_position' => 'LABA/RUGI'],
            ['account_code' => '50-33-50100', 'category' => 'BIAYA GAJI', 'account_name' => 'BIAYA GAJI KARYAWAN', 'balance_position' => 'Kr', 'report_position' => 'LABA/RUGI']
        ];
        
        foreach ($sample_accounts as $account) {
            try {
                $coa->addAccount($account);
            } catch (Exception $e) {
                // Skip if already exists
            }
        }
        echo "<p style='color: green;'>✓ Data sample berhasil dibuat</p>";
    }
    
    // 3. Verify Installation
    echo "<h3>3. Verifikasi Instalasi...</h3>";
    $stats = $coa->getStatistics();
    echo "<p>Total akun: <strong>{$stats['total_accounts']}</strong></p>";
    
    foreach ($stats['by_report'] as $report) {
        echo "<p>{$report['report_position']}: <strong>{$report['count']}</strong> akun</p>";
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>✓ Setup Berhasil!</h3>";
    echo "<p>Sistem akuntansi siap digunakan.</p>";
    echo "<p><a href='admin/index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Masuk ke Admin Panel</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ Setup Gagal!</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Pastikan:</p>";
    echo "<ul>";
    echo "<li>MySQL server berjalan</li>";
    echo "<li>Username dan password database benar (config/database.php)</li>";
    echo "<li>PHP memiliki permission untuk membuat database</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h2, h3 {
    color: #333;
}

p {
    margin: 10px 0;
}

details {
    margin: 10px 0;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 5px;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #ddd;
}
</style>