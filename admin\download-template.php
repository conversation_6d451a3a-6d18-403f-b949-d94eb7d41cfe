<?php
/**
 * Download CSV Template
 * Generate template CSV untuk import
 */

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="template_chart_of_accounts.csv"');

// Create output stream
$output = fopen('php://output', 'w');

// Write BOM for UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Write header
fputcsv($output, [
    'no. account',
    'KATEGORI', 
    'no. account',
    'NAMA COA',
    'POS SALDO',
    'POS LAPORAN',
    '',
    '',
    ''
], ';');

// Write sample data
$sample_data = [
    ['1-01-11000', '', '1-01-11000', 'KAS BESAR', 'Db', 'NERACA', '', '', ''],
    ['1-02-11001', '', '1-02-11001', 'KAS KECIL', 'Db', 'NERACA', '', '', ''],
    ['1-03-12001', 'BANK', '1-03-12001', 'BANK BCA', 'Db', 'NERACA', '', '', ''],
    ['1-04-13000', 'PIUTANG USAHA', '1-04-13000', 'PIUTANG DAGANG', 'Db', 'NERACA', '', '', ''],
    ['4-15-21001', 'HUTANG USAHA', '4-15-21001', 'HUTANG DAGANG', 'Kr', 'NERACA', '', '', ''],
    ['6-24-31001', 'MODAL DISETOR', '6-24-31001', 'MODAL SAHAM', 'Kr', 'NERACA', '', '', ''],
    ['7-29-40001', '', '7-29-40001', 'PENDAPATAN PENJUALAN', 'Kr', 'LABA/RUGI', '', '', ''],
    ['50-33-50100', 'BIAYA GAJI', '50-33-50100', 'BIAYA GAJI KARYAWAN', 'Kr', 'LABA/RUGI', '', '', '']
];

foreach ($sample_data as $row) {
    fputcsv($output, $row, ';');
}

fclose($output);
exit;
?>