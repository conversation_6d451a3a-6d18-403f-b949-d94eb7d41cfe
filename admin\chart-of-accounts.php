<?php
/**
 * Chart of Accounts Management
 * Mengelola Master Data Akun
 */

session_start();
require_once '../classes/ChartOfAccounts.php';

$coa = new ChartOfAccounts();
$message = '';
$message_type = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                try {
                    $result = $coa->addAccount($_POST);
                    if ($result) {
                        $message = 'Akun berhasil ditambahkan!';
                        $message_type = 'success';
                    }
                } catch (Exception $e) {
                    $message = 'Error: ' . $e->getMessage();
                    $message_type = 'danger';
                }
                break;
                
            case 'update':
                try {
                    $result = $coa->updateAccount($_POST['id'], $_POST);
                    if ($result) {
                        $message = 'Akun berhasil diupdate!';
                        $message_type = 'success';
                    }
                } catch (Exception $e) {
                    $message = 'Error: ' . $e->getMessage();
                    $message_type = 'danger';
                }
                break;
                
            case 'delete':
                try {
                    $result = $coa->deleteAccount($_POST['id']);
                    if ($result) {
                        $message = 'Akun berhasil dihapus!';
                        $message_type = 'success';
                    }
                } catch (Exception $e) {
                    $message = 'Error: ' . $e->getMessage();
                    $message_type = 'danger';
                }
                break;
        }
    }
}

// Get filters
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$report_position = $_GET['report_position'] ?? '';

// Get data
$accounts = $coa->getAllAccounts($search, $category, $report_position);
$categories = $coa->getCategories();
$stats = $coa->getStatistics();

// Get account for editing
$edit_account = null;
if (isset($_GET['edit'])) {
    $edit_account = $coa->getAccountByCode($_GET['edit']);
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart of Accounts - Sistem Akuntansi</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
        }

        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .btn-elegant {
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-elegant:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: var(--primary-color);
        }

        .badge-position {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .badge-db {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .badge-kr {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }

        .badge-neraca {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .badge-laba {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .filter-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .modal-content {
            border-radius: 15px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4">
                        <h4 class="text-white mb-4">
                            <i class="bi bi-calculator"></i> Akuntansi
                        </h4>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-speedometer2 me-2"></i> Dashboard
                        </a>
                        <a class="nav-link active" href="chart-of-accounts.php">
                            <i class="bi bi-list-ul me-2"></i> Chart of Accounts
                        </a>
                        <a class="nav-link" href="journal-entries.php">
                            <i class="bi bi-journal-text me-2"></i> Jurnal Umum
                        </a>
                        <a class="nav-link" href="trial-balance.php">
                            <i class="bi bi-table me-2"></i> Trial Balance
                        </a>
                        <a class="nav-link" href="balance-sheet.php">
                            <i class="bi bi-bar-chart me-2"></i> Neraca
                        </a>
                        <a class="nav-link" href="income-statement.php">
                            <i class="bi bi-graph-up me-2"></i> Laba Rugi
                        </a>
                        <a class="nav-link" href="bank-transactions.php">
                            <i class="bi bi-bank me-2"></i> Buku Bank
                        </a>
                        <a class="nav-link" href="reports.php">
                            <i class="bi bi-file-earmark-text me-2"></i> Laporan
                        </a>
                        <hr class="text-white-50">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear me-2"></i> Pengaturan
                        </a>
                        <a class="nav-link" href="import-export.php">
                            <i class="bi bi-arrow-down-up me-2"></i> Import/Export
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">Chart of Accounts</h1>
                            <p class="text-muted">Kelola master data akun perusahaan</p>
                        </div>
                        <div>
                            <button class="btn btn-success btn-elegant" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                                <i class="bi bi-plus-circle me-2"></i> Tambah Akun
                            </button>
                            <a href="import-export.php" class="btn btn-primary btn-elegant">
                                <i class="bi bi-upload me-2"></i> Import CSV
                            </a>
                        </div>
                    </div>

                    <!-- Alert Messages -->
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="content-card text-center">
                                <h3 class="text-primary"><?php echo number_format($stats['total_accounts']); ?></h3>
                                <p class="text-muted mb-0">Total Akun</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="content-card text-center">
                                <h3 class="text-success">
                                    <?php 
                                    $neraca_count = 0;
                                    foreach($stats['by_report'] as $report) {
                                        if($report['report_position'] == 'NERACA') {
                                            $neraca_count = $report['count'];
                                            break;
                                        }
                                    }
                                    echo number_format($neraca_count);
                                    ?>
                                </h3>
                                <p class="text-muted mb-0">Akun Neraca</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="content-card text-center">
                                <h3 class="text-warning">
                                    <?php 
                                    $laba_count = 0;
                                    foreach($stats['by_report'] as $report) {
                                        if($report['report_position'] == 'LABA/RUGI') {
                                            $laba_count = $report['count'];
                                            break;
                                        }
                                    }
                                    echo number_format($laba_count);
                                    ?>
                                </h3>
                                <p class="text-muted mb-0">Akun Laba/Rugi</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="content-card text-center">
                                <h3 class="text-info"><?php echo count($categories); ?></h3>
                                <p class="text-muted mb-0">Kategori</p>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="filter-card">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Pencarian</label>
                                <input type="text" class="form-control" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Cari kode atau nama akun...">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Kategori</label>
                                <select class="form-select" name="category">
                                    <option value="">Semua Kategori</option>
                                    <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>" 
                                            <?php echo $category == $cat ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Posisi Laporan</label>
                                <select class="form-select" name="report_position">
                                    <option value="">Semua Posisi</option>
                                    <option value="NERACA" <?php echo $report_position == 'NERACA' ? 'selected' : ''; ?>>NERACA</option>
                                    <option value="LABA/RUGI" <?php echo $report_position == 'LABA/RUGI' ? 'selected' : ''; ?>>LABA/RUGI</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-light btn-elegant">
                                        <i class="bi bi-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Accounts Table -->
                    <div class="content-card">
                        <div class="table-responsive">
                            <table class="table table-hover" id="accountsTable">
                                <thead>
                                    <tr>
                                        <th>Kode Akun</th>
                                        <th>Nama Akun</th>
                                        <th>Kategori</th>
                                        <th>Posisi Saldo</th>
                                        <th>Posisi Laporan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($accounts as $account): ?>
                                    <tr>
                                        <td><code><?php echo htmlspecialchars($account['account_code']); ?></code></td>
                                        <td><?php echo htmlspecialchars($account['account_name']); ?></td>
                                        <td>
                                            <?php if ($account['category']): ?>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($account['category']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge-position badge-<?php echo strtolower($account['balance_position']); ?>">
                                                <?php echo $account['balance_position']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge-position badge-<?php echo $account['report_position'] == 'NERACA' ? 'neraca' : 'laba'; ?>">
                                                <?php echo $account['report_position']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" 
                                                        onclick="editAccount('<?php echo $account['account_code']; ?>')"
                                                        data-bs-toggle="modal" data-bs-target="#editAccountModal">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" 
                                                        onclick="deleteAccount(<?php echo $account['id']; ?>, '<?php echo htmlspecialchars($account['account_name']); ?>')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Account Modal -->
    <div class="modal fade" id="addAccountModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Akun Baru</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="mb-3">
                            <label class="form-label">Kode Akun *</label>
                            <input type="text" class="form-control" name="account_code" required 
                                   placeholder="Contoh: 1-01-11000">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Nama Akun *</label>
                            <input type="text" class="form-control" name="account_name" required 
                                   placeholder="Contoh: KAS BESAR">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Kategori</label>
                            <input type="text" class="form-control" name="category" 
                                   placeholder="Contoh: AKTIVA LANCAR">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Posisi Saldo *</label>
                                    <select class="form-select" name="balance_position" required>
                                        <option value="Db">Debit (Db)</option>
                                        <option value="Kr">Kredit (Kr)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Posisi Laporan *</label>
                                    <select class="form-select" name="report_position" required>
                                        <option value="NERACA">NERACA</option>
                                        <option value="LABA/RUGI">LABA/RUGI</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-success">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Account Modal -->
    <div class="modal fade" id="editAccountModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Akun</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editAccountForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="id" id="edit_id">
                        
                        <div class="mb-3">
                            <label class="form-label">Kode Akun</label>
                            <input type="text" class="form-control" id="edit_account_code" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Nama Akun *</label>
                            <input type="text" class="form-control" name="account_name" id="edit_account_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Kategori</label>
                            <input type="text" class="form-control" name="category" id="edit_category">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Posisi Saldo *</label>
                                    <select class="form-select" name="balance_position" id="edit_balance_position" required>
                                        <option value="Db">Debit (Db)</option>
                                        <option value="Kr">Kredit (Kr)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Posisi Laporan *</label>
                                    <select class="form-select" name="report_position" id="edit_report_position" required>
                                        <option value="NERACA">NERACA</option>
                                        <option value="LABA/RUGI">LABA/RUGI</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Form (Hidden) -->
    <form method="POST" id="deleteForm" style="display: none;">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="id" id="delete_id">
    </form>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Initialize DataTable
        $(document).ready(function() {
            $('#accountsTable').DataTable({
                "pageLength": 25,
                "order": [[ 0, "asc" ]],
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.6/i18n/id.json"
                }
            });
        });

        // Edit Account Function
        function editAccount(accountCode) {
            // Fetch account data via AJAX
            fetch(`get-account.php?code=${accountCode}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const account = data.account;
                        document.getElementById('edit_id').value = account.id;
                        document.getElementById('edit_account_code').value = account.account_code;
                        document.getElementById('edit_account_name').value = account.account_name;
                        document.getElementById('edit_category').value = account.category || '';
                        document.getElementById('edit_balance_position').value = account.balance_position;
                        document.getElementById('edit_report_position').value = account.report_position;
                    } else {
                        alert('Error loading account data');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading account data');
                });
        }

        // Delete Account Function
        function deleteAccount(id, name) {
            if (confirm(`Apakah Anda yakin ingin menghapus akun "${name}"?`)) {
                document.getElementById('delete_id').value = id;
                document.getElementById('deleteForm').submit();
            }
        }
    </script>
</body>
</html>