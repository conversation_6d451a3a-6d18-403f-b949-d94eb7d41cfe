<?php
/**
 * Admin Dashboard - Sistem Akuntansi
 * Elegant Admin Interface
 */

session_start();
require_once '../classes/ChartOfAccounts.php';
require_once '../classes/Journal.php';

// Initialize classes
$coa = new ChartOfAccounts();
$journal = new Journal();

// Get statistics
$coa_stats = $coa->getStatistics();
$journal_stats = $journal->getStatistics();

// Get recent journal entries
$recent_journals = $journal->getAllJournalEntries(5, 0);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Sistem Akuntansi</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-text: #2c3e50;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid var(--secondary-color);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.success {
            border-left-color: var(--success-color);
        }

        .stat-card.warning {
            border-left-color: var(--warning-color);
        }

        .stat-card.danger {
            border-left-color: var(--danger-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--dark-text);
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-top: 20px;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-top: 20px;
        }

        .btn-elegant {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-elegant:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-posted {
            background-color: #d4edda;
            color: #155724;
        }

        .status-draft {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4">
                        <h4 class="text-white mb-4">
                            <i class="bi bi-calculator"></i> Akuntansi
                        </h4>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="index.php">
                            <i class="bi bi-speedometer2 me-2"></i> Dashboard
                        </a>
                        <a class="nav-link" href="chart-of-accounts.php">
                            <i class="bi bi-list-ul me-2"></i> Chart of Accounts
                        </a>
                        <a class="nav-link" href="journal-entries.php">
                            <i class="bi bi-journal-text me-2"></i> Jurnal Umum
                        </a>
                        <a class="nav-link" href="trial-balance.php">
                            <i class="bi bi-table me-2"></i> Trial Balance
                        </a>
                        <a class="nav-link" href="balance-sheet.php">
                            <i class="bi bi-bar-chart me-2"></i> Neraca
                        </a>
                        <a class="nav-link" href="income-statement.php">
                            <i class="bi bi-graph-up me-2"></i> Laba Rugi
                        </a>
                        <a class="nav-link" href="bank-transactions.php">
                            <i class="bi bi-bank me-2"></i> Buku Bank
                        </a>
                        <a class="nav-link" href="reports.php">
                            <i class="bi bi-file-earmark-text me-2"></i> Laporan
                        </a>
                        <hr class="text-white-50">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear me-2"></i> Pengaturan
                        </a>
                        <a class="nav-link" href="import-export.php">
                            <i class="bi bi-arrow-down-up me-2"></i> Import/Export
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">Dashboard</h1>
                            <p class="text-muted">Selamat datang di Sistem Akuntansi</p>
                        </div>
                        <div>
                            <span class="text-muted">
                                <i class="bi bi-calendar3"></i> 
                                <?php echo date('d F Y'); ?>
                            </span>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="stat-card">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="stat-number"><?php echo number_format($coa_stats['total_accounts']); ?></div>
                                        <div class="stat-label">Total Akun</div>
                                    </div>
                                    <div class="text-primary">
                                        <i class="bi bi-list-ul" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="stat-card success">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="stat-number"><?php echo number_format($journal_stats['total_entries']); ?></div>
                                        <div class="stat-label">Total Jurnal</div>
                                    </div>
                                    <div class="text-success">
                                        <i class="bi bi-journal-text" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="stat-card warning">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="stat-number"><?php echo number_format($journal_stats['this_month']); ?></div>
                                        <div class="stat-label">Jurnal Bulan Ini</div>
                                    </div>
                                    <div class="text-warning">
                                        <i class="bi bi-calendar-month" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="stat-card danger">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <div class="stat-number">
                                            <?php 
                                            $posted_count = 0;
                                            foreach($journal_stats['by_status'] as $status) {
                                                if($status['status'] == 'posted') {
                                                    $posted_count = $status['count'];
                                                    break;
                                                }
                                            }
                                            echo number_format($posted_count);
                                            ?>
                                        </div>
                                        <div class="stat-label">Jurnal Posted</div>
                                    </div>
                                    <div class="text-danger">
                                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h5 class="mb-3">Distribusi Akun</h5>
                                <canvas id="accountChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h5 class="mb-3">Status Jurnal</h5>
                                <canvas id="journalChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Journal Entries -->
                    <div class="table-container">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">Jurnal Terbaru</h5>
                            <a href="journal-entries.php" class="btn btn-primary btn-elegant btn-sm">
                                Lihat Semua <i class="bi bi-arrow-right"></i>
                            </a>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tanggal</th>
                                        <th>No. Referensi</th>
                                        <th>Deskripsi</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($recent_journals)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="bi bi-inbox" style="font-size: 2rem;"></i><br>
                                            Belum ada jurnal yang dibuat
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                        <?php foreach ($recent_journals as $journal): ?>
                                        <tr>
                                            <td><?php echo date('d/m/Y', strtotime($journal['entry_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($journal['reference_no']); ?></td>
                                            <td><?php echo htmlspecialchars(substr($journal['description'], 0, 50)) . '...'; ?></td>
                                            <td>Rp <?php echo number_format($journal['total_debit'], 0, ',', '.'); ?></td>
                                            <td>
                                                <span class="status-badge status-<?php echo $journal['status']; ?>">
                                                    <?php echo ucfirst($journal['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="journal-detail.php?id=<?php echo $journal['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="chart-container">
                                <h5 class="mb-3">Aksi Cepat</h5>
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="journal-entries.php?action=add" class="btn btn-success btn-elegant w-100">
                                            <i class="bi bi-plus-circle me-2"></i> Buat Jurnal
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="chart-of-accounts.php?action=add" class="btn btn-primary btn-elegant w-100">
                                            <i class="bi bi-plus-circle me-2"></i> Tambah Akun
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="trial-balance.php" class="btn btn-info btn-elegant w-100">
                                            <i class="bi bi-table me-2"></i> Trial Balance
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="import-export.php" class="btn btn-warning btn-elegant w-100">
                                            <i class="bi bi-upload me-2"></i> Import Data
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Charts -->
    <script>
        // Account Distribution Chart
        const accountCtx = document.getElementById('accountChart').getContext('2d');
        const accountChart = new Chart(accountCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php foreach($coa_stats['by_report'] as $report): ?>
                    '<?php echo $report['report_position']; ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    data: [
                        <?php foreach($coa_stats['by_report'] as $report): ?>
                        <?php echo $report['count']; ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: [
                        '#3498db',
                        '#27ae60',
                        '#f39c12',
                        '#e74c3c'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Journal Status Chart
        const journalCtx = document.getElementById('journalChart').getContext('2d');
        const journalChart = new Chart(journalCtx, {
            type: 'bar',
            data: {
                labels: [
                    <?php foreach($journal_stats['by_status'] as $status): ?>
                    '<?php echo ucfirst($status['status']); ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    label: 'Jumlah Jurnal',
                    data: [
                        <?php foreach($journal_stats['by_status'] as $status): ?>
                        <?php echo $status['count']; ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: [
                        '#f39c12',
                        '#27ae60'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>